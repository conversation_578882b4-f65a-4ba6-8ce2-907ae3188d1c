# 四路电机驱动程序文档

## 项目概述

本项目是基于德州仪器(TI) MSPM0G3507微控制器的四路电机驱动控制系统。该系统通过UART串口通信协议控制四个电机，支持多种电机类型，并能实时获取编码器数据和电机速度信息。

## 硬件平台

- **微控制器**: TI MSPM0G3507
- **开发板**: LP-MSPM0G3507 LaunchPad
- **开发环境**: Keil MDK
- **通信接口**: 双UART串口（UART0用于调试输出，UART1用于电机通信）

## 系统架构

### 文件结构
```
├── empty.c                    # 主程序文件
├── ti_msp_dl_config.h/c      # TI驱动库配置文件
├── BSP/                      # 板级支持包
│   ├── app_motor_usart.h/c   # 电机控制应用层
│   ├── bsp_motor_usart.h/c   # 电机串口底层驱动
│   ├── usart.h/c             # 调试串口驱动
│   └── delay.h/c             # 延时函数
└── ti/                       # TI官方驱动库文件
```

## 主要功能模块

### 1. 电机类型支持

系统支持5种不同类型的电机：

| 电机类型 | 编号 | 减速比 | 磁环线数 | 轮径(mm) | 死区值 |
|---------|------|--------|----------|----------|--------|
| 520电机 | 1 | 30 | 11 | 67.00 | 1900 |
| 310电机 | 2 | 20 | 13 | 48.00 | 1600 |
| 速码盘TT电机 | 3 | 45 | 13 | 68.00 | 1250 |
| TT直流减速电机 | 4 | 48 | - | - | 1000 |
| L型520电机 | 5 | 40 | 11 | 67.00 | 1900 |

### 2. 数据上传模式

系统提供3种数据上传模式：

- **模式0**: 不接收数据
- **模式1**: 接收编码器总计数据
- **模式2**: 接收实时编码器增量数据
- **模式3**: 接收当前电机速度(mm/s)

### 3. 通信协议

#### 发送命令格式
所有发送到电机驱动板的命令都采用以下格式：
```
$命令:参数#
```

#### 主要发送命令

| 命令 | 格式 | 功能 |
|------|------|------|
| mtype | `$mtype:类型#` | 设置电机类型 |
| deadzone | `$deadzone:值#` | 设置电机死区 |
| mline | `$mline:线数#` | 设置磁环线数 |
| mphase | `$mphase:比值#` | 设置减速比 |
| wdiameter | `$wdiameter:直径#` | 设置轮径 |
| mpid | `$mpid:P,I,D#` | 设置PID参数 |
| upload | `$upload:总计,实时,速度#` | 设置数据上传开关 |
| spd | `$spd:M1,M2,M3,M4#` | 控制四个电机速度 |
| pwm | `$pwm:M1,M2,M3,M4#` | 控制四个电机PWM |

#### 接收数据格式

| 数据类型 | 格式 | 说明 |
|----------|------|------|
| 编码器总计 | `MAll:值1,值2,值3,值4` | 四个电机的编码器累计值 |
| 实时编码器 | `MTEP:值1,值2,值3,值4` | 四个电机的10ms增量值 |
| 电机速度 | `MSPD:值1,值2,值3,值4` | 四个电机的当前速度 |

## 核心函数说明

### 主程序流程 (empty.c)

```c
int main(void)
{
    // 1. 初始化串口通信
    USART_Init();
    
    // 2. 关闭数据上传
    send_upload_data(false,false,false);
    
    // 3. 根据电机类型配置参数
    #if MOTOR_TYPE == 1
        send_motor_type(1);           // 设置电机类型
        send_pulse_phase(30);         // 设置减速比
        send_pulse_line(11);          // 设置磁环线数
        send_wheel_diameter(67.00);   // 设置轮径
        send_motor_deadzone(1900);    // 设置死区
    #endif
    
    // 4. 开启指定的数据上传模式
    send_upload_data(true,false,false);
    
    // 5. 主循环：控制电机并处理反馈数据
    while(1)
    {
        if(g_recv_flag == 1)  // 收到数据标志
        {
            // 控制电机速度/PWM
            Contrl_Speed(i*10,i*10,i*10,i*10);
            
            // 处理接收到的数据
            Deal_data_real();
            
            // 打印反馈数据
            printf("M1:%d,M2:%d,M3:%d,M4:%d\r\n", 
                   Encoder_Now[0],Encoder_Now[1],
                   Encoder_Now[2],Encoder_Now[3]);
        }
    }
}
```

### 电机控制函数

#### 速度控制
```c
void Contrl_Speed(int16_t M1_speed, int16_t M2_speed, 
                  int16_t M3_speed, int16_t M4_speed)
```
- **功能**: 控制四个电机的速度
- **参数**: 速度值范围 -1000 到 1000
- **格式**: `$spd:M1,M2,M3,M4#`

#### PWM控制
```c
void Contrl_Pwm(int16_t M1_pwm, int16_t M2_pwm, 
                int16_t M3_pwm, int16_t M4_pwm)
```
- **功能**: 直接控制四个电机的PWM输出
- **参数**: PWM占空比值
- **格式**: `$pwm:M1,M2,M3,M4#`

### 数据处理函数

#### 串口数据解析
```c
void Deal_Control_Rxtemp(uint8_t rxtemp)
```
- **功能**: 解析从电机驱动板接收的单个字节
- **协议**: 以'$'开始，以'#'结束的数据包
- **状态机**: 使用状态机方式解析完整数据包

#### 数据格式化处理
```c
void Deal_data_real(void)
```
- **功能**: 将接收到的字符串数据转换为数值
- **支持格式**: 
  - MAll: 编码器总计数据
  - MTEP: 实时编码器数据  
  - MSPD: 电机速度数据

## 全局变量

```c
// 编码器数据
extern int Encoder_Offset[4];    // 实时编码器增量
extern int Encoder_Now[4];       // 编码器累计值
extern float g_Speed[4];         // 电机速度(mm/s)
extern uint8_t g_recv_flag;      // 数据接收标志
```

## 配置说明

### 编译时配置

在`empty.c`文件顶部可以配置：

```c
#define UPLOAD_DATA 1    // 数据上传模式选择
#define MOTOR_TYPE 1     // 电机类型选择
```

### 电机参数配置

不同电机类型需要配置不同的参数：
- **减速比** (pulse_phase): 影响速度计算精度
- **磁环线数** (pulse_line): 影响编码器分辨率  
- **轮径** (wheel_diameter): 影响线速度计算
- **死区值** (deadzone): 消除电机启动死区

## 使用方法

1. **硬件连接**: 将MSPM0G3507开发板与四路电机驱动板通过UART1连接
2. **编译下载**: 使用Keil MDK编译并下载程序到开发板
3. **串口监控**: 通过UART0(调试串口)监控系统运行状态
4. **参数调整**: 根据实际电机修改电机类型和相关参数
5. **功能测试**: 观察电机运行和数据反馈是否正常

## 注意事项

1. **波特率设置**: 确保串口波特率配置正确
2. **电机参数**: 不同电机的参数必须准确配置，否则影响控制精度
3. **数据同步**: 发送命令后需要适当延时，确保电机驱动板正确接收
4. **中断处理**: 串口中断处理函数不能被阻塞，避免数据丢失
5. **缓冲区管理**: 注意接收缓冲区溢出保护

## 扩展功能

系统预留了PID参数设置接口，可以根据需要调整电机控制的PID参数：

```c
void send_motor_PID(float P, float I, float D)
```

通过调用此函数可以实时调整电机的控制参数，优化控制效果。
