<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [..\OBJ\empty.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image ..\OBJ\empty.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6190004: Last Updated: Mon Feb 17 21:54:23 2025
<BR><P>
<H3>Maximum Stack Usage =        592 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; Deal_data_real &rArr; atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">SVC_Handler</a><BR>
 <LI><a href="#[4]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">PendSV_Handler</a><BR>
 <LI><a href="#[5]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">SysTick_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[a]">ADC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[b]">ADC1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1b]">AES_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[c]">CANFD0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[d]">DAC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1d]">DMA_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[6]">GROUP0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[7]">GROUP1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[19]">I2C0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1a]">I2C1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[4]">PendSV_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1c]">RTC_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[e]">SPI0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[f]">SPI1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[3]">SVC_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[5]">SysTick_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[15]">TIMA0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[16]">TIMA1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[13]">TIMG0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[18]">TIMG12_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[14]">TIMG6_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[17]">TIMG7_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[8]">TIMG8_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[12]">UART0_IRQHandler</a> from usart.o(.text.UART0_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[10]">UART1_IRQHandler</a> from bsp_motor_usart.o(.text.UART1_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[11]">UART2_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[9]">UART3_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1f]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_mspm0g350x_uvision.o(.text)
 <LI><a href="#[21]">_sbackspace</a> from _sgetc.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[20]">_sgetc</a> from _sgetc.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[24]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[23]">fputc</a> from usart.o(.text.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[22]">isspace</a> from isspace_o.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[1e]">main</a> from empty.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[1f]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(.text)
</UL>
<P><STRONG><a name="[93]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[25]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[41]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[94]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[95]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[96]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[97]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[98]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>ADC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>ADC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>AES_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>CANFD0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>DAC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>Default_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>

<P><STRONG><a name="[6]"></a>GROUP0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>GROUP1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>I2C0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>I2C1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TIMA0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TIMA1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TIMG0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TIMG12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TIMG6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>TIMG7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>TIMG8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>UART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Deal_Control_Rxtemp
</UL>

<P><STRONG><a name="[99]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[9a]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[29]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[9b]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[9c]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[28]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Deal_data_real
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Deal_Control_Rxtemp
</UL>

<P><STRONG><a name="[9d]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[9e]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[2a]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[4b]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Deal_data_real
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Contrl_Speed
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_motor_deadzone
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_wheel_diameter
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_pulse_line
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_pulse_phase
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_motor_type
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_upload_data
</UL>

<P><STRONG><a name="[59]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Deal_data_real
</UL>

<P><STRONG><a name="[57]"></a>strncmp</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Deal_data_real
</UL>

<P><STRONG><a name="[88]"></a>strtok</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, strtok.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = strtok
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;splitString
</UL>

<P><STRONG><a name="[2b]"></a>atoi</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, atoi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = atoi &rArr; strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Deal_data_real
</UL>

<P><STRONG><a name="[87]"></a>__aeabi_f2d</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_wheel_diameter
</UL>

<P><STRONG><a name="[2e]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Deal_data_real
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[9f]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, uidiv_div0.o(.text), UNUSED)

<P><STRONG><a name="[90]"></a>__aeabi_uidivmod</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, uidiv_div0.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[30]"></a>__aeabi_uldivmod</STRONG> (Thumb, 96 bytes, Stack size 48 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[35]"></a>__strtod_int</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atof
</UL>

<P><STRONG><a name="[2d]"></a>strtol</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, strtol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[a0]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[2f]"></a>_float_round</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[a1]"></a>_float_epilogue</STRONG> (Thumb, 114 bytes, Stack size 12 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[38]"></a>__aeabi_dadd</STRONG> (Thumb, 328 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[3c]"></a>__aeabi_dsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[3d]"></a>__aeabi_drsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[3e]"></a>__aeabi_dmul</STRONG> (Thumb, 202 bytes, Stack size 72 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[3f]"></a>__aeabi_ddiv</STRONG> (Thumb, 234 bytes, Stack size 40 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[40]"></a>__aeabi_d2ulz</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[8d]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[26]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[a2]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[32]"></a>__aeabi_llsl</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, llshl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[a3]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[31]"></a>__aeabi_llsr</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, llushr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[a4]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[39]"></a>__aeabi_lasr</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[a5]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[36]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[22]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace_o.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = isspace
</UL>
<BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[34]"></a>_scanf_real</STRONG> (Thumb, 0 bytes, Stack size 104 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>

<P><STRONG><a name="[45]"></a>_scanf_really_real</STRONG> (Thumb, 584 bytes, Stack size 104 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[20]"></a>_sgetc</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[21]"></a>_sbackspace</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[37]"></a>_strtoul</STRONG> (Thumb, 166 bytes, Stack size 40 bytes, _strtoul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[3b]"></a>_double_round</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[3a]"></a>_double_epilogue</STRONG> (Thumb, 164 bytes, Stack size 48 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_clz
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[43]"></a>__aeabi_lmul</STRONG> (Thumb, 48 bytes, Stack size 12 bytes, llmul.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[a6]"></a>_ll_mul</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, llmul.o(.text), UNUSED)

<P><STRONG><a name="[47]"></a>_chval</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
</UL>

<P><STRONG><a name="[44]"></a>__aeabi_ul2d</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, dfltul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[49]"></a>Contrl_Speed</STRONG> (Thumb, 80 bytes, Stack size 48 bytes, app_motor_usart.o(.text.Contrl_Speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Contrl_Speed &rArr; Send_Motor_ArrayU8 &rArr; Send_Motor_U8 &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sprintf
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Motor_ArrayU8
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[71]"></a>DL_Common_delayCycles</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dl_common.o(.text.DL_Common_delayCycles))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Common_delayCycles
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[4f]"></a>DL_UART_init</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, dl_uart.o(.text.DL_UART_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_UART_init &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[67]"></a>DL_UART_setClockConfig</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, dl_uart.o(.text.DL_UART_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[54]"></a>Deal_Control_Rxtemp</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, app_motor_usart.o(.text.Deal_Control_Rxtemp))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Deal_Control_Rxtemp
</UL>
<BR>[Calls]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
</UL>

<P><STRONG><a name="[56]"></a>Deal_data_real</STRONG> (Thumb, 564 bytes, Stack size 328 bytes, app_motor_usart.o(.text.Deal_data_real))
<BR><BR>[Stack]<UL><LI>Max Depth = 552<LI>Call Chain = Deal_data_real &rArr; atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atof
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;splitString
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5b]"></a>SYSCFG_DL_GPIO_init</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_GPIO_init &rArr; DL_GPIO_initPeripheralInputFunction
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initPeripheralInputFunction
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initPeripheralOutputFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[5e]"></a>SYSCFG_DL_SYSCTL_init</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SYSCFG_DL_SYSCTL_init &rArr; DL_SYSCTL_setSYSOSCFreq &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_enableMFCLK
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_disableSYSPLL
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_disableHFXT
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setSYSOSCFreq
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setBORThreshold
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[63]"></a>SYSCFG_DL_SYSTICK_init</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SYSCFG_DL_SYSTICK_init &rArr; DL_SYSTICK_init
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSTICK_enable
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSTICK_init
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[66]"></a>SYSCFG_DL_UART_0_init</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_UART_0_init &rArr; DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enable
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableInterrupt
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[6a]"></a>SYSCFG_DL_UART_1_init</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_UART_1_init &rArr; DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enable
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableInterrupt
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[6b]"></a>SYSCFG_DL_init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SYSCFG_DL_init &rArr; SYSCFG_DL_UART_1_init &rArr; DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[6c]"></a>SYSCFG_DL_initPower</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_initPower))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SYSCFG_DL_initPower &rArr; DL_Common_delayCycles
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_delayCycles
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enablePower
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enablePower
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_reset
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[4c]"></a>Send_Motor_ArrayU8</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, bsp_motor_usart.o(.text.Send_Motor_ArrayU8))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Send_Motor_ArrayU8 &rArr; Send_Motor_U8 &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Motor_U8
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Contrl_Speed
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_motor_deadzone
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_wheel_diameter
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_pulse_line
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_pulse_phase
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_motor_type
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_upload_data
</UL>

<P><STRONG><a name="[72]"></a>Send_Motor_U8</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, bsp_motor_usart.o(.text.Send_Motor_U8))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Send_Motor_U8 &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_transmitData
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_isBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Motor_ArrayU8
</UL>

<P><STRONG><a name="[12]"></a>UART0_IRQHandler</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, usart.o(.text.UART0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = UART0_IRQHandler &rArr; DL_UART_receiveData
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_receiveData
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_getPendingInterrupt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>UART1_IRQHandler</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, bsp_motor_usart.o(.text.UART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART1_IRQHandler &rArr; Deal_Control_Rxtemp
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_receiveData
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_getPendingInterrupt
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Deal_Control_Rxtemp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[79]"></a>USART_Init</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, usart.o(.text.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USART_Init &rArr; SYSCFG_DL_init &rArr; SYSCFG_DL_UART_1_init &rArr; DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7c]"></a>delay_ms</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, delay.o(.text.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = delay_ms &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7d]"></a>delay_us</STRONG> (Thumb, 108 bytes, Stack size 20 bytes, delay.o(.text.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[23]"></a>fputc</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, usart.o(.text.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_transmitData
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_isBusy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[1e]"></a>main</STRONG> (Thumb, 208 bytes, Stack size 40 bytes, empty.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 592<LI>Call Chain = main &rArr; Deal_data_real &rArr; atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Deal_data_real
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Contrl_Speed
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_motor_deadzone
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_wheel_diameter
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_pulse_line
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_pulse_phase
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_motor_type
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_upload_data
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[86]"></a>send_motor_deadzone</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, app_motor_usart.o(.text.send_motor_deadzone))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = send_motor_deadzone &rArr; Send_Motor_ArrayU8 &rArr; Send_Motor_U8 &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sprintf
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Motor_ArrayU8
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[82]"></a>send_motor_type</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, app_motor_usart.o(.text.send_motor_type))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = send_motor_type &rArr; Send_Motor_ArrayU8 &rArr; Send_Motor_U8 &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sprintf
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Motor_ArrayU8
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[84]"></a>send_pulse_line</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, app_motor_usart.o(.text.send_pulse_line))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = send_pulse_line &rArr; Send_Motor_ArrayU8 &rArr; Send_Motor_U8 &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sprintf
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Motor_ArrayU8
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[83]"></a>send_pulse_phase</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, app_motor_usart.o(.text.send_pulse_phase))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = send_pulse_phase &rArr; Send_Motor_ArrayU8 &rArr; Send_Motor_U8 &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sprintf
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Motor_ArrayU8
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[81]"></a>send_upload_data</STRONG> (Thumb, 68 bytes, Stack size 32 bytes, app_motor_usart.o(.text.send_upload_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = send_upload_data &rArr; Send_Motor_ArrayU8 &rArr; Send_Motor_U8 &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sprintf
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Motor_ArrayU8
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[85]"></a>send_wheel_diameter</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, app_motor_usart.o(.text.send_wheel_diameter))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = send_wheel_diameter &rArr; Send_Motor_ArrayU8 &rArr; Send_Motor_U8 &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sprintf
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Motor_ArrayU8
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[58]"></a>splitString</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, app_motor_usart.o(.text.splitString))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = splitString &rArr; strtok
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtok
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Deal_data_real
</UL>

<P><STRONG><a name="[89]"></a>__0printf</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[a7]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[a8]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[a9]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[80]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8b]"></a>__0sprintf</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[aa]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[ab]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[ac]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[4a]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Contrl_Speed
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_motor_deadzone
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_wheel_diameter
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_pulse_line
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_pulse_phase
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_motor_type
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_upload_data
</UL>

<P><STRONG><a name="[48]"></a>__ARM_clz</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, depilogue.o(i.__ARM_clz), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[2c]"></a>__aeabi_errno_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr))
<BR><BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[ad]"></a>__rt_errno_addr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr), UNUSED)

<P><STRONG><a name="[91]"></a>__read_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__read_errno))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atof
</UL>

<P><STRONG><a name="[ae]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[af]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[b0]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[92]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atof
</UL>

<P><STRONG><a name="[46]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, scanf_fp.o(i._is_digit), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[5a]"></a>atof</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, atof.o(i.atof))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Deal_data_real
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[6d]"></a>DL_GPIO_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[6e]"></a>DL_UART_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[6f]"></a>DL_GPIO_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[70]"></a>DL_UART_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[5c]"></a>DL_GPIO_initPeripheralOutputFunction</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_initPeripheralOutputFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[5d]"></a>DL_GPIO_initPeripheralInputFunction</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_initPeripheralInputFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[5f]"></a>DL_SYSCTL_setBORThreshold</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SYSCTL_setBORThreshold
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[4d]"></a>DL_SYSCTL_setSYSOSCFreq</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setSYSOSCFreq &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[60]"></a>DL_SYSCTL_disableHFXT</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[61]"></a>DL_SYSCTL_disableSYSPLL</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[62]"></a>DL_SYSCTL_enableMFCLK</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[53]"></a>DL_UART_setOversampling</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_UART_setOversampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_UART_setOversampling &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[52]"></a>DL_UART_setBaudRateDivisor</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[68]"></a>DL_UART_enableInterrupt</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_UART_enableInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[69]"></a>DL_UART_enable</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[64]"></a>DL_SYSTICK_init</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SYSTICK_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SYSTICK_init
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
</UL>

<P><STRONG><a name="[65]"></a>DL_SYSTICK_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSTICK_enable))
<BR><BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
</UL>

<P><STRONG><a name="[4e]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setSYSOSCFreq
</UL>

<P><STRONG><a name="[50]"></a>DL_UART_disable</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, dl_uart.o(.text.DL_UART_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
</UL>

<P><STRONG><a name="[51]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_uart.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
</UL>

<P><STRONG><a name="[7a]"></a>__NVIC_ClearPendingIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, usart.o(.text.__NVIC_ClearPendingIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[7b]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, usart.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[7e]"></a>DL_UART_isBusy</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, usart.o(.text.DL_UART_isBusy))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_isBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[7f]"></a>DL_UART_transmitData</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, usart.o(.text.DL_UART_transmitData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_transmitData
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[75]"></a>DL_UART_getPendingInterrupt</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, usart.o(.text.DL_UART_getPendingInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_getPendingInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[76]"></a>DL_UART_receiveData</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, usart.o(.text.DL_UART_receiveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_receiveData
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[73]"></a>DL_UART_isBusy</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, bsp_motor_usart.o(.text.DL_UART_isBusy))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_isBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Motor_U8
</UL>

<P><STRONG><a name="[74]"></a>DL_UART_transmitData</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, bsp_motor_usart.o(.text.DL_UART_transmitData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_transmitData
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Motor_U8
</UL>

<P><STRONG><a name="[77]"></a>DL_UART_getPendingInterrupt</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, bsp_motor_usart.o(.text.DL_UART_getPendingInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_getPendingInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
</UL>

<P><STRONG><a name="[78]"></a>DL_UART_receiveData</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, bsp_motor_usart.o(.text.DL_UART_receiveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_receiveData
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
</UL>

<P><STRONG><a name="[8c]"></a>_fp_digits</STRONG> (Thumb, 344 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[8a]"></a>_printf_core</STRONG> (Thumb, 1754 bytes, Stack size 128 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[8f]"></a>_printf_post_padding</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[8e]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 40 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[24]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL>
<P><STRONG><a name="[33]"></a>_local_sscanf</STRONG> (Thumb, 64 bytes, Stack size 56 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>

<P><STRONG><a name="[42]"></a>_fp_value</STRONG> (Thumb, 286 bytes, Stack size 72 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lmul
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
